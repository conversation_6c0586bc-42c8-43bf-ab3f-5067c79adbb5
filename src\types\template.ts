export interface Template {
  id: string;
  name: string;
  description: string;
  placeholders: string[];
  uploadedAt: string;
  pathToDocx: string;
  pathToImage: string;
}

export interface TemplateUploadRequest {
  templateName: string;
  templateDescription: string;
  file: File;
}

export interface TemplateUploadResponse {
  success: boolean;
  message: string;
  template?: Template;
}

export interface DocumentOption {
  title: string;
  description: string;
  href: string;
  icon: any;
}
