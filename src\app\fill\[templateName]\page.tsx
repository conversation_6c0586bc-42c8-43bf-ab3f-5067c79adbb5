"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { FileText, ArrowRight, ArrowLeft } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { Template } from "@/types/template";

export default function FillTemplatePage() {
  const params = useParams();
  const router = useRouter();
  const templateName = params.templateName as string;
  
  const [template, setTemplate] = useState<Template | null>(null);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const fetchTemplate = async () => {
      try {
        const response = await fetch('/api/templates');
        const result = await response.json();
        
        if (result.success) {
          const foundTemplate = result.templates.find((t: Template) => t.id === templateName);
          if (foundTemplate) {
            setTemplate(foundTemplate);
            // Initialize form data with empty values
            const initialData: Record<string, string> = {};
            foundTemplate.placeholders.forEach(placeholder => {
              initialData[placeholder] = '';
            });
            setFormData(initialData);
          } else {
            toast.error("Template not found");
            router.push('/');
          }
        }
      } catch (error) {
        console.error('Error fetching template:', error);
        toast.error("Failed to load template");
        router.push('/');
      } finally {
        setLoading(false);
      }
    };

    if (templateName) {
      fetchTemplate();
    }
  }, [templateName, router]);

  const handleInputChange = (placeholder: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [placeholder]: value
    }));
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    // Check if all fields are filled
    const emptyFields = Object.entries(formData).filter(([_, value]) => !value.trim());
    if (emptyFields.length > 0) {
      toast.error("Please fill in all fields");
      return;
    }

    setIsSubmitting(true);

    try {
      // Store form data in sessionStorage for the preview page
      sessionStorage.setItem(`template_data_${templateName}`, JSON.stringify(formData));
      
      // Navigate to preview page
      router.push(`/preview/${templateName}`);
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error("An error occurred while processing your data");
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatPlaceholderLabel = (placeholder: string) => {
    return placeholder
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <div className="text-center py-12">
          <div className="text-muted-foreground">Loading template...</div>
        </div>
      </div>
    );
  }

  if (!template) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <div className="text-center py-12">
          <div className="text-muted-foreground">Template not found</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      {/* Page Header */}
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-semibold text-foreground mb-2">
          Fill {template.name}
        </h1>
        <p className="text-muted-foreground text-lg">
          {template.description}
        </p>
      </div>

      {/* Fill Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Certificate Information
          </CardTitle>
          <CardDescription>
            Please fill in all the required information for your certificate
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Dynamic Input Fields */}
            <div className="grid gap-4 md:grid-cols-2">
              {template.placeholders.map((placeholder) => (
                <div key={placeholder} className="space-y-2">
                  <Label htmlFor={placeholder}>
                    {formatPlaceholderLabel(placeholder)}
                  </Label>
                  <Input
                    id={placeholder}
                    type="text"
                    placeholder={`Enter ${formatPlaceholderLabel(placeholder).toLowerCase()}`}
                    value={formData[placeholder] || ''}
                    onChange={(e) => handleInputChange(placeholder, e.target.value)}
                    required
                  />
                </div>
              ))}
            </div>

            {/* Action Buttons */}
            <div className="flex gap-4 pt-4">
              <Link href="/" className="flex-1">
                <Button type="button" variant="outline" className="w-full">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Home
                </Button>
              </Link>
              
              <Button 
                type="submit" 
                className="flex-1" 
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <ArrowRight className="h-4 w-4 mr-2 animate-pulse" />
                    Processing...
                  </>
                ) : (
                  <>
                    <ArrowRight className="h-4 w-4 mr-2" />
                    Preview Certificate
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
