import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import Docxtemplater from 'docxtemplater';
import <PERSON><PERSON><PERSON><PERSON> from 'pizzip';
import mammoth from 'mammoth';

export async function POST(request: NextRequest) {
  try {
    const { templateId, data, action } = await request.json();

    if (!templateId || !data || !action) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get template file path
    const templatePath = path.join(process.cwd(), 'public', 'templates', templateId, 'template.docx');
    
    // Check if template file exists
    try {
      await fs.access(templatePath);
    } catch (error) {
      return NextResponse.json(
        { success: false, message: 'Template file not found' },
        { status: 404 }
      );
    }

    // Read the template file
    const templateBuffer = await fs.readFile(templatePath);
    
    // Create a new zip instance
    const zip = new <PERSON>z<PERSON>ip(templateBuffer);
    
    // Create docxtemplater instance
    const doc = new Docxtemplater(zip, {
      paragraphLoop: true,
      linebreaks: true,
    });

    // Fill the template with data
    doc.render(data);

    // Get the filled document buffer
    const filledBuffer = doc.getZip().generate({
      type: 'nodebuffer',
      compression: 'DEFLATE',
    });

    if (action === 'preview') {
      // Generate preview image
      const previewUrl = await generatePreviewImage(filledBuffer, templateId);
      
      return NextResponse.json({
        success: true,
        previewUrl
      });
    } else if (action === 'pdf') {
      // For now, we'll return the DOCX file as PDF functionality requires additional setup
      // In production, you would convert DOCX to PDF here
      
      return new NextResponse(filledBuffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'Content-Disposition': `attachment; filename="${templateId}_filled.docx"`,
        },
      });
    }

    return NextResponse.json(
      { success: false, message: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error processing template:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to process template' },
      { status: 500 }
    );
  }
}

async function generatePreviewImage(docxBuffer: Buffer, templateId: string): Promise<string> {
  try {
    // Convert DOCX to HTML using mammoth
    const { value: htmlContent } = await mammoth.convertToHtml({ buffer: docxBuffer });
    
    // Create a simple HTML page with the content
    const fullHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <style>
            body {
              font-family: 'Times New Roman', serif;
              max-width: 800px;
              margin: 0 auto;
              padding: 40px;
              line-height: 1.6;
              background: white;
            }
            h1, h2, h3 {
              text-align: center;
              margin: 20px 0;
            }
            p {
              margin: 10px 0;
              text-align: justify;
            }
            .signature-line {
              border-bottom: 1px solid #000;
              width: 200px;
              margin: 20px auto;
            }
          </style>
        </head>
        <body>
          ${htmlContent}
        </body>
      </html>
    `;

    // Save HTML to a temporary file for preview
    const previewDir = path.join(process.cwd(), 'public', 'previews');
    await fs.mkdir(previewDir, { recursive: true });
    
    const timestamp = Date.now();
    const previewFileName = `${templateId}_${timestamp}.html`;
    const previewPath = path.join(previewDir, previewFileName);
    
    await fs.writeFile(previewPath, fullHtml);
    
    // Return the URL to access the preview
    return `/previews/${previewFileName}`;
    
  } catch (error) {
    console.error('Error generating preview:', error);
    
    // Return a fallback preview URL
    return '/templates/default-preview.png';
  }
}
