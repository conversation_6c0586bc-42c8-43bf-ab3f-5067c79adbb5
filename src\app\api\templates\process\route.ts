import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import Docxtemplater from 'docxtemplater';
import <PERSON><PERSON><PERSON><PERSON> from 'pizzip';
import mammoth from 'mammoth';

// Function to clean up old preview files
async function cleanupOldPreviews(templateId: string) {
  try {
    const previewDir = path.join(process.cwd(), 'public', 'previews');
    const files = await fs.readdir(previewDir);

    // Find and delete old preview files for this template
    const oldFiles = files.filter(file =>
      file.startsWith(`${templateId}_`) && file.endsWith('.html')
    );

    for (const file of oldFiles) {
      const filePath = path.join(previewDir, file);
      await fs.unlink(filePath);
      console.log('Deleted old preview file:', file);
    }
  } catch (error) {
    console.warn('Error cleaning up old previews:', error);
  }
}

export async function POST(request: NextRequest) {
  try {
    const { templateId, data, action } = await request.json();

    if (!templateId || !data || !action) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get template file path
    const templatePath = path.join(process.cwd(), 'public', 'templates', templateId, 'template.docx');
    
    // Check if template file exists
    try {
      await fs.access(templatePath);
    } catch (error) {
      return NextResponse.json(
        { success: false, message: 'Template file not found' },
        { status: 404 }
      );
    }

    // Read the template file
    const templateBuffer = await fs.readFile(templatePath);
    
    // Create a new zip instance
    const zip = new PizZip(templateBuffer);
    
    // Create docxtemplater instance
    const doc = new Docxtemplater(zip, {
      paragraphLoop: true,
      linebreaks: true,
    });

    // Fill the template with data
    doc.render(data);

    // Get the filled document buffer
    const filledBuffer = doc.getZip().generate({
      type: 'nodebuffer',
      compression: 'DEFLATE',
    });

    if (action === 'preview') {
      // Clean up old preview files first
      await cleanupOldPreviews(templateId);

      // Generate preview content
      const { previewUrl, previewContent } = await generatePreviewImage(filledBuffer, templateId);

      console.log('Generated preview URL:', previewUrl);

      return NextResponse.json({
        success: true,
        previewUrl,
        previewContent
      });
    } else if (action === 'pdf') {
      // For now, we'll return the DOCX file as PDF functionality requires additional setup
      // In production, you would convert DOCX to PDF here
      
      return new NextResponse(Buffer.from(filledBuffer), {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'Content-Disposition': `attachment; filename="${templateId}_filled.docx"`,
        },
      });
    }

    return NextResponse.json(
      { success: false, message: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error processing template:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to process template' },
      { status: 500 }
    );
  }
}

async function generatePreviewImage(docxBuffer: Buffer, templateId: string): Promise<{previewUrl: string, previewContent: string}> {
  try {
    console.log('=== STARTING PREVIEW GENERATION FOR TEMPLATE:', templateId, '===');

    // Convert DOCX to HTML and apply proper formatting
    const { value: rawHtmlContent } = await mammoth.convertToHtml({ buffer: docxBuffer });
    console.log('Raw mammoth HTML:', rawHtmlContent.substring(0, 200));

    // Apply document-specific formatting
    const htmlContent = applyDocumentFormatting(rawHtmlContent);

    console.log('HTML content generated, length:', htmlContent.length);

    // Create a properly formatted HTML page with A4 document styling
    const fullHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            @page {
              size: A4;
              margin: 1in;
            }

            * {
              box-sizing: border-box;
            }

            body {
              font-family: 'Times New Roman', serif;
              font-size: 12pt;
              line-height: 1.5;
              color: #000;
              background: white;
              margin: 0;
              padding: 0;
              width: 210mm; /* A4 width */
              min-height: 297mm; /* A4 height */
              margin: 0 auto;
              padding: 25.4mm; /* 1 inch margins */
              box-shadow: 0 0 10px rgba(0,0,0,0.1);
            }

            /* Paragraph styles */
            p {
              margin: 0 0 12pt 0;
              text-align: left;
              text-indent: 0;
              orphans: 2;
              widows: 2;
            }

            /* Document structure specific styles */
            .document-header {
              text-align: center !important;
              font-weight: normal;
              margin: 0 0 6pt 0;
              font-size: 12pt;
            }

            .document-title {
              text-align: center !important;
              font-weight: bold;
              margin: 24pt 0 24pt 0;
              font-size: 14pt;
              letter-spacing: 2pt;
            }

            .body-text {
              text-align: justify !important;
              text-indent: 36pt;
              margin: 12pt 0;
              line-height: 1.5;
            }

            .signature-section {
              text-align: center !important;
              margin: 24pt 0 12pt 0;
            }

            .date-section {
              text-align: justify !important;
              margin: 12pt 0;
              text-indent: 36pt;
            }

            .official-section {
              text-align: justify !important;
              margin: 18pt 0;
              text-indent: 36pt;
              font-weight: bold;
            }

            .normal-text {
              text-align: left;
              margin: 6pt 0;
            }

            /* Heading styles */
            h1 {
              font-size: 16pt;
              font-weight: bold;
              text-align: center;
              margin: 24pt 0 12pt 0;
              page-break-after: avoid;
            }

            h2 {
              font-size: 14pt;
              font-weight: bold;
              text-align: center;
              margin: 18pt 0 12pt 0;
              page-break-after: avoid;
            }

            h3 {
              font-size: 12pt;
              font-weight: bold;
              margin: 12pt 0 6pt 0;
              page-break-after: avoid;
            }

            /* Text formatting */
            strong, b {
              font-weight: bold;
            }

            em, i {
              font-style: italic;
            }

            u {
              text-decoration: underline;
            }

            /* Alignment classes from Word document */
            .center-align, .center, [style*="text-align: center"], [style*="text-align:center"] {
              text-align: center !important;
              margin-left: auto !important;
              margin-right: auto !important;
            }

            .right-align, .right, [style*="text-align: right"], [style*="text-align:right"] {
              text-align: right !important;
              margin-left: auto !important;
              margin-right: 0 !important;
            }

            .left-align, .left, [style*="text-align: left"], [style*="text-align:left"] {
              text-align: left !important;
              margin-left: 0 !important;
              margin-right: auto !important;
            }

            .justify-align, .justify, [style*="text-align: justify"], [style*="text-align:justify"] {
              text-align: justify !important;
              text-justify: inter-word;
            }

            /* Additional alignment detection */
            p[align="center"], div[align="center"] {
              text-align: center !important;
            }

            p[align="right"], div[align="right"] {
              text-align: right !important;
            }

            p[align="left"], div[align="left"] {
              text-align: left !important;
            }

            p[align="justify"], div[align="justify"] {
              text-align: justify !important;
            }

            /* Indentation */
            .indent-1 { margin-left: 36pt; }
            .indent-2 { margin-left: 72pt; }
            .indent-3 { margin-left: 108pt; }

            /* Spacing */
            .single-space { line-height: 1.0; }
            .double-space { line-height: 2.0; }
            .space-before { margin-top: 12pt; }
            .space-after { margin-bottom: 12pt; }

            /* Table styles */
            table {
              border-collapse: collapse;
              width: 100%;
              margin: 12pt 0;
            }

            td, th {
              border: 1pt solid #000;
              padding: 6pt;
              text-align: left;
              vertical-align: top;
            }

            /* Signature lines */
            .signature-line {
              border-bottom: 1pt solid #000;
              width: 200pt;
              margin: 24pt auto;
              text-align: center;
            }

            /* Page breaks */
            .page-break {
              page-break-before: always;
            }

            /* Print styles */
            @media print {
              body {
                box-shadow: none;
                margin: 0;
                padding: 1in;
              }

              .no-print {
                display: none;
              }
            }

            /* Preserve original Word formatting */
            [style*="margin-left"] {
              /* Preserve left margins from Word */
            }

            [style*="margin-right"] {
              /* Preserve right margins from Word */
            }

            [style*="text-indent"] {
              /* Preserve text indentation from Word */
            }
          </style>
        </head>
        <body>
          ${htmlContent}
        </body>
      </html>
    `;

    // Save HTML to a temporary file for preview
    const previewDir = path.join(process.cwd(), 'public', 'previews');
    await fs.mkdir(previewDir, { recursive: true });

    const timestamp = Date.now();
    const previewFileName = `${templateId}_${timestamp}.html`;
    const previewPath = path.join(previewDir, previewFileName);

    console.log('Saving preview to:', previewPath);

    await fs.writeFile(previewPath, fullHtml);

    const previewUrl = `/previews/${previewFileName}`;
    console.log('Preview saved successfully, URL:', previewUrl);

    // Return both URL and content
    return {
      previewUrl,
      previewContent: htmlContent
    };

  } catch (error) {
    console.error('Error generating preview:', error);

    // Return a fallback
    return {
      previewUrl: '/templates/default-preview.png',
      previewContent: '<p>Error generating preview</p>'
    };
  }
}

// Apply document-specific formatting to HTML content
function applyDocumentFormatting(htmlContent: string): string {
  let formattedContent = htmlContent;

  console.log('Applying document formatting...');

  // Apply formatting based on content patterns
  formattedContent = formattedContent.replace(
    /<p>([^<]*(?:Republic of the Philippines|Province of|MUNICIPALITY OF)[^<]*)<\/p>/gi,
    '<p style="text-align: center !important; font-weight: normal; margin: 0 0 6pt 0; font-size: 12pt;">$1</p>'
  );

  // Format AFFIDAVIT title
  formattedContent = formattedContent.replace(
    /<p>([^<]*AFFIDAVIT[^<]*)<\/p>/gi,
    '<p style="text-align: center !important; font-weight: bold; margin: 24pt 0; font-size: 14pt; letter-spacing: 2pt;">$1</p>'
  );

  // Format signature lines
  formattedContent = formattedContent.replace(
    /<p>([^<]*(?:Affiant|HON\.|Municipal Mayor|CTC #|Issued On)[^<]*)<\/p>/gi,
    '<p style="text-align: center !important; margin: 24pt 0 12pt 0;">$1</p>'
  );

  // Format body text (long paragraphs)
  formattedContent = formattedContent.replace(
    /<p>([^<]{50,})<\/p>/gi,
    '<p style="text-align: justify !important; text-indent: 36pt; margin: 12pt 0; line-height: 1.5;">$1</p>'
  );

  // Format official statements
  formattedContent = formattedContent.replace(
    /<p>([^<]*(?:IN WITNESS WHEREOF|SUBSCRIBED AND SWORN)[^<]*)<\/p>/gi,
    '<p style="text-align: justify !important; margin: 18pt 0; text-indent: 36pt; font-weight: bold;">$1</p>'
  );

  console.log('Document formatting applied');
  return formattedContent;
}




