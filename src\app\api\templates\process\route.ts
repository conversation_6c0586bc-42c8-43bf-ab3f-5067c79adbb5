import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import Docxtemplater from 'docxtemplater';
import <PERSON><PERSON><PERSON><PERSON> from 'pizzip';
import mammoth from 'mammoth';

// Function to clean up old preview files
async function cleanupOldPreviews(templateId: string) {
  try {
    const previewDir = path.join(process.cwd(), 'public', 'previews');
    const files = await fs.readdir(previewDir);

    // Find and delete old preview files for this template
    const oldFiles = files.filter(file =>
      file.startsWith(`${templateId}_`) && file.endsWith('.html')
    );

    for (const file of oldFiles) {
      const filePath = path.join(previewDir, file);
      await fs.unlink(filePath);
      console.log('Deleted old preview file:', file);
    }
  } catch (error) {
    console.warn('Error cleaning up old previews:', error);
  }
}

export async function POST(request: NextRequest) {
  try {
    const { templateId, data, action } = await request.json();

    if (!templateId || !data || !action) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get template file path
    const templatePath = path.join(process.cwd(), 'public', 'templates', templateId, 'template.docx');
    
    // Check if template file exists
    try {
      await fs.access(templatePath);
    } catch (error) {
      return NextResponse.json(
        { success: false, message: 'Template file not found' },
        { status: 404 }
      );
    }

    // Read the template file
    const templateBuffer = await fs.readFile(templatePath);
    
    // Create a new zip instance
    const zip = new PizZip(templateBuffer);
    
    // Create docxtemplater instance
    const doc = new Docxtemplater(zip, {
      paragraphLoop: true,
      linebreaks: true,
    });

    // Fill the template with data
    doc.render(data);

    // Get the filled document buffer
    const filledBuffer = doc.getZip().generate({
      type: 'nodebuffer',
      compression: 'DEFLATE',
    });

    if (action === 'preview') {
      // Clean up old preview files first
      await cleanupOldPreviews(templateId);

      // Generate preview content
      const { previewUrl, previewContent } = await generatePreviewImage(filledBuffer, templateId);

      console.log('Generated preview URL:', previewUrl);

      return NextResponse.json({
        success: true,
        previewUrl,
        previewContent
      });
    } else if (action === 'pdf') {
      // For now, we'll return the DOCX file as PDF functionality requires additional setup
      // In production, you would convert DOCX to PDF here
      
      return new NextResponse(filledBuffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'Content-Disposition': `attachment; filename="${templateId}_filled.docx"`,
        },
      });
    }

    return NextResponse.json(
      { success: false, message: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error processing template:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to process template' },
      { status: 500 }
    );
  }
}

async function generatePreviewImage(docxBuffer: Buffer, templateId: string): Promise<{previewUrl: string, previewContent: string}> {
  try {
    console.log('Starting preview generation for template:', templateId);

    // Convert DOCX to HTML using mammoth with comprehensive style mapping and formatting preservation
    const { value: htmlContent } = await mammoth.convertToHtml({
      buffer: docxBuffer,
      styleMap: [
        "p[style-name='Normal'] => p:fresh",
        "p[style-name='Title'] => h1.title:fresh",
        "p[style-name='Heading 1'] => h1:fresh",
        "p[style-name='Heading 2'] => h2:fresh",
        "p[style-name='Heading 3'] => h3:fresh",
        "r[style-name='Strong'] => strong",
        "r[style-name='Emphasis'] => em",
        "p[style-name='Header'] => p.header:fresh",
        "p[style-name='Footer'] => p.footer:fresh"
      ],
      convertImage: mammoth.images.imgElement(function(image) {
        return image.read("base64").then(function(imageBuffer) {
          return {
            src: "data:" + image.contentType + ";base64," + imageBuffer
          };
        });
      }),
      includeDefaultStyleMap: true,
      includeEmbeddedStyleMap: true,
      transformDocument: mammoth.transforms.paragraph(function(element) {
        // Preserve alignment information
        if (element.alignment) {
          element.styleName = element.styleName || "Normal";
          // Add alignment class based on Word alignment
          switch (element.alignment) {
            case "center":
              return { ...element, styleId: "center-align" };
            case "right":
              return { ...element, styleId: "right-align" };
            case "justify":
              return { ...element, styleId: "justify-align" };
            case "left":
            default:
              return { ...element, styleId: "left-align" };
          }
        }
        return element;
      })
    });

    console.log('HTML content generated, length:', htmlContent.length);

    // Post-process HTML to enhance alignment detection
    const processedHtmlContent = enhanceTextAlignment(htmlContent);

    // Create a properly formatted HTML page with A4 document styling
    const fullHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            @page {
              size: A4;
              margin: 1in;
            }

            * {
              box-sizing: border-box;
            }

            body {
              font-family: 'Times New Roman', serif;
              font-size: 12pt;
              line-height: 1.5;
              color: #000;
              background: white;
              margin: 0;
              padding: 0;
              width: 210mm; /* A4 width */
              min-height: 297mm; /* A4 height */
              margin: 0 auto;
              padding: 25.4mm; /* 1 inch margins */
              box-shadow: 0 0 10px rgba(0,0,0,0.1);
            }

            /* Paragraph styles */
            p {
              margin: 0 0 12pt 0;
              text-align: justify;
              text-indent: 0;
              orphans: 2;
              widows: 2;
            }

            /* Heading styles */
            h1 {
              font-size: 16pt;
              font-weight: bold;
              text-align: center;
              margin: 24pt 0 12pt 0;
              page-break-after: avoid;
            }

            h2 {
              font-size: 14pt;
              font-weight: bold;
              text-align: center;
              margin: 18pt 0 12pt 0;
              page-break-after: avoid;
            }

            h3 {
              font-size: 12pt;
              font-weight: bold;
              margin: 12pt 0 6pt 0;
              page-break-after: avoid;
            }

            /* Text formatting */
            strong, b {
              font-weight: bold;
            }

            em, i {
              font-style: italic;
            }

            u {
              text-decoration: underline;
            }

            /* Alignment classes from Word document */
            .center-align, .center, [style*="text-align: center"], [style*="text-align:center"] {
              text-align: center !important;
              margin-left: auto !important;
              margin-right: auto !important;
            }

            .right-align, .right, [style*="text-align: right"], [style*="text-align:right"] {
              text-align: right !important;
              margin-left: auto !important;
              margin-right: 0 !important;
            }

            .left-align, .left, [style*="text-align: left"], [style*="text-align:left"] {
              text-align: left !important;
              margin-left: 0 !important;
              margin-right: auto !important;
            }

            .justify-align, .justify, [style*="text-align: justify"], [style*="text-align:justify"] {
              text-align: justify !important;
              text-justify: inter-word;
            }

            /* Additional alignment detection */
            p[align="center"], div[align="center"] {
              text-align: center !important;
            }

            p[align="right"], div[align="right"] {
              text-align: right !important;
            }

            p[align="left"], div[align="left"] {
              text-align: left !important;
            }

            p[align="justify"], div[align="justify"] {
              text-align: justify !important;
            }

            /* Indentation */
            .indent-1 { margin-left: 36pt; }
            .indent-2 { margin-left: 72pt; }
            .indent-3 { margin-left: 108pt; }

            /* Spacing */
            .single-space { line-height: 1.0; }
            .double-space { line-height: 2.0; }
            .space-before { margin-top: 12pt; }
            .space-after { margin-bottom: 12pt; }

            /* Table styles */
            table {
              border-collapse: collapse;
              width: 100%;
              margin: 12pt 0;
            }

            td, th {
              border: 1pt solid #000;
              padding: 6pt;
              text-align: left;
              vertical-align: top;
            }

            /* Signature lines */
            .signature-line {
              border-bottom: 1pt solid #000;
              width: 200pt;
              margin: 24pt auto;
              text-align: center;
            }

            /* Page breaks */
            .page-break {
              page-break-before: always;
            }

            /* Print styles */
            @media print {
              body {
                box-shadow: none;
                margin: 0;
                padding: 1in;
              }

              .no-print {
                display: none;
              }
            }

            /* Preserve original Word formatting */
            [style*="margin-left"] {
              /* Preserve left margins from Word */
            }

            [style*="margin-right"] {
              /* Preserve right margins from Word */
            }

            [style*="text-indent"] {
              /* Preserve text indentation from Word */
            }
          </style>
        </head>
        <body>
          ${processedHtmlContent}
        </body>
      </html>
    `;

    // Save HTML to a temporary file for preview
    const previewDir = path.join(process.cwd(), 'public', 'previews');
    await fs.mkdir(previewDir, { recursive: true });

    const timestamp = Date.now();
    const previewFileName = `${templateId}_${timestamp}.html`;
    const previewPath = path.join(previewDir, previewFileName);

    console.log('Saving preview to:', previewPath);

    await fs.writeFile(previewPath, fullHtml);

    const previewUrl = `/previews/${previewFileName}`;
    console.log('Preview saved successfully, URL:', previewUrl);

    // Return both URL and content
    return {
      previewUrl,
      previewContent: processedHtmlContent
    };

  } catch (error) {
    console.error('Error generating preview:', error);

    // Return a fallback
    return {
      previewUrl: '/templates/default-preview.png',
      previewContent: '<p>Error generating preview</p>'
    };
  }
}

// Function to enhance text alignment detection in HTML content
function enhanceTextAlignment(htmlContent: string): string {
  let processedContent = htmlContent;

  // Detect and enhance center alignment patterns
  processedContent = processedContent.replace(
    /<p([^>]*)>\s*<strong[^>]*>\s*([^<]*)\s*<\/strong>\s*<\/p>/gi,
    (match, attrs, content) => {
      // If content looks like a title or header, make it centered
      if (content.length < 100 && (
        content.toUpperCase() === content ||
        /^[A-Z\s]+$/.test(content) ||
        /(CERTIFICATE|AFFIDAVIT|REPUBLIC|PHILIPPINES)/i.test(content)
      )) {
        return `<p${attrs} class="center-align"><strong>${content}</strong></p>`;
      }
      return match;
    }
  );

  // Detect paragraphs that should be centered (short, all caps, or titles)
  processedContent = processedContent.replace(
    /<p([^>]*)>([^<]+)<\/p>/gi,
    (match, attrs, content) => {
      const trimmedContent = content.trim();
      if (trimmedContent.length < 80 && (
        trimmedContent.toUpperCase() === trimmedContent ||
        /(REPUBLIC OF THE PHILIPPINES|CERTIFICATE|AFFIDAVIT|BARANGAY|MUNICIPALITY)/i.test(trimmedContent)
      )) {
        return `<p${attrs} class="center-align">${content}</p>`;
      }
      return match;
    }
  );

  // Detect signature lines and date lines (usually right-aligned)
  processedContent = processedContent.replace(
    /<p([^>]*)>([^<]*(?:signature|date|signed|day of|this|day|month|year)[^<]*)<\/p>/gi,
    (match, attrs, content) => {
      if (content.includes('_____') || content.includes('____') || /\b\d{1,2}\w{0,2}\s+day\s+of\b/i.test(content)) {
        return `<p${attrs} class="right-align">${content}</p>`;
      }
      return match;
    }
  );

  // Detect indented paragraphs
  processedContent = processedContent.replace(
    /<p([^>]*)>\s{4,}([^<]+)<\/p>/gi,
    '<p$1 class="indent-1">$2</p>'
  );

  return processedContent;
}
