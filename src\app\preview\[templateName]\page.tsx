"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { FileText, Edit, Download, ArrowLeft } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { Template } from "@/types/template";

export default function PreviewTemplatePage() {
  const params = useParams();
  const router = useRouter();
  const templateName = params.templateName as string;

  const [template, setTemplate] = useState<Template | null>(null);
  const [formData, setFormData] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(true);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [previewContent, setPreviewContent] = useState<string | null>(null);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  useEffect(() => {
    const fetchTemplateAndData = async () => {
      try {
        // Get form data from sessionStorage
        const storedData = sessionStorage.getItem(
          `template_data_${templateName}`
        );
        if (!storedData) {
          toast.error("No form data found. Please fill the form first.");
          router.push(`/fill/${templateName}`);
          return;
        }

        const parsedData = JSON.parse(storedData);
        setFormData(parsedData);

        // Fetch template info
        const response = await fetch("/api/templates");
        const result = await response.json();

        if (result.success) {
          const foundTemplate = result.templates.find(
            (t: Template) => t.id === templateName
          );
          if (foundTemplate) {
            setTemplate(foundTemplate);

            // Generate document preview
            await generatePreview(foundTemplate, parsedData);
          } else {
            toast.error("Template not found");
            router.push("/");
          }
        }
      } catch (error) {
        console.error("Error loading preview:", error);
        toast.error("Failed to load preview");
        router.push("/");
      } finally {
        setLoading(false);
      }
    };

    if (templateName) {
      fetchTemplateAndData();
    }
  }, [templateName, router]);

  const generatePreview = async (
    template: Template,
    data: Record<string, string>
  ) => {
    try {
      console.log(
        "Generating preview for template:",
        template.id,
        "with data:",
        data
      );

      const response = await fetch("/api/templates/process", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          templateId: template.id,
          data: data,
          action: "preview",
        }),
      });

      const result = await response.json();

      console.log("Preview API response:", result);

      if (result.success && result.previewContent) {
        console.log("Setting preview content and URL:", result.previewUrl);
        setPreviewUrl(result.previewUrl);
        setPreviewContent(result.previewContent);
      } else {
        console.error("Preview generation failed:", result);
        toast.error("Failed to generate preview");
      }
    } catch (error) {
      console.error("Error generating preview:", error);
      toast.error("Failed to generate preview");
    }
  };

  const handleSaveAsPDF = async () => {
    if (!template) return;

    setIsGeneratingPDF(true);

    try {
      const response = await fetch("/api/templates/process", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          templateId: template.id,
          data: formData,
          action: "pdf",
        }),
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${template.name.replace(
          /[^a-zA-Z0-9]/g,
          "_"
        )}_Certificate.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        toast.success("PDF downloaded successfully!");
      } else {
        toast.error("Failed to generate PDF");
      }
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast.error("Failed to generate PDF");
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="text-center py-12">
          <div className="text-muted-foreground">Generating preview...</div>
        </div>
      </div>
    );
  }

  if (!template) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="text-center py-12">
          <div className="text-muted-foreground">Template not found</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* Page Header */}
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-semibold text-foreground mb-2">
          Preview {template.name}
        </h1>
        <p className="text-muted-foreground text-lg">
          Review your certificate before downloading
        </p>
      </div>

      {/* Preview Card */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Certificate Preview
          </CardTitle>
          <CardDescription>
            This is how your certificate will look when printed
          </CardDescription>
        </CardHeader>
        <CardContent>
          {previewContent ? (
            <div className="border rounded-lg overflow-hidden bg-gray-100 p-4">
              <div
                className="preview-content bg-white shadow-lg mx-auto"
                dangerouslySetInnerHTML={{ __html: previewContent }}
                style={{
                  width: "210mm", // A4 width
                  minHeight: "297mm", // A4 height
                  padding: "25.4mm", // 1 inch margins
                  fontFamily: "'Times New Roman', serif",
                  color: "#000",
                  transform: "scale(0.7)", // Scale down for better fit
                  transformOrigin: "top center",
                  margin: "0 auto",
                }}
              />
            </div>
          ) : (
            <div className="border rounded-lg p-12 text-center bg-muted/20">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                Preview is being generated...
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex gap-4">
        <Link href={`/fill/${templateName}`} className="flex-1">
          <Button variant="outline" className="w-full">
            <Edit className="h-4 w-4 mr-2" />
            Edit Information
          </Button>
        </Link>

        <Button
          onClick={handleSaveAsPDF}
          className="flex-1"
          disabled={isGeneratingPDF || !previewContent}
        >
          {isGeneratingPDF ? (
            <>
              <Download className="h-4 w-4 mr-2 animate-pulse" />
              Generating PDF...
            </>
          ) : (
            <>
              <Download className="h-4 w-4 mr-2" />
              Save as PDF
            </>
          )}
        </Button>
      </div>

      {/* Back to Home */}
      <div className="text-center mt-6">
        <Link href="/">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Button>
        </Link>
      </div>
    </div>
  );
}
