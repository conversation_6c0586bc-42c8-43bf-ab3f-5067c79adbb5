import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import mammoth from 'mammoth';
import { addTemplate, generateTemplateId, extractPlaceholders } from '@/lib/templates';
import { Template } from '@/types/template';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const templateName = formData.get('templateName') as string;
    const templateDescription = formData.get('templateDescription') as string;
    const file = formData.get('file') as File;

    if (!templateName || !templateDescription || !file) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    if (!file.name.endsWith('.docx')) {
      return NextResponse.json(
        { success: false, message: 'Only DOCX files are allowed' },
        { status: 400 }
      );
    }

    const templateId = generateTemplateId(templateName);
    const templateDir = path.join(process.cwd(), 'public', 'templates', templateId);
    
    // Create template directory
    await fs.mkdir(templateDir, { recursive: true });

    // Save the DOCX file
    const docxPath = path.join(templateDir, 'template.docx');
    const arrayBuffer = await file.arrayBuffer();
    await fs.writeFile(docxPath, Buffer.from(arrayBuffer));

    // Extract text content to find placeholders
    const { value: textContent } = await mammoth.extractRawText({ buffer: Buffer.from(arrayBuffer) });
    const placeholders = extractPlaceholders(textContent);

    // Generate preview image (placeholder for now - would need actual implementation)
    const previewPath = path.join(templateDir, 'preview.png');
    // For now, create a placeholder file - in production, you'd generate an actual preview
    await fs.writeFile(previewPath, Buffer.from('placeholder'));

    // Create template object
    const template: Template = {
      id: templateId,
      name: templateName,
      description: templateDescription,
      placeholders,
      uploadedAt: new Date().toISOString(),
      pathToDocx: `/templates/${templateId}/template.docx`,
      pathToImage: `/templates/${templateId}/preview.png`
    };

    // Add template to JSON file
    await addTemplate(template);

    return NextResponse.json({
      success: true,
      message: 'Template uploaded successfully',
      template
    });

  } catch (error) {
    console.error('Error uploading template:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to upload template' },
      { status: 500 }
    );
  }
}
