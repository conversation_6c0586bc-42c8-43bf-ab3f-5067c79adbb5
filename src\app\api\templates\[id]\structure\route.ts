import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

interface DocumentElement {
  id: string;
  type: 'text' | 'field' | 'signature' | 'date';
  content: string;
  alignment: 'left' | 'center' | 'right' | 'justify';
  fontSize: number;
  fontWeight: 'normal' | 'bold';
  margin: {
    top: number;
    bottom: number;
  };
  textIndent: number;
}

interface TemplateStructure {
  templateId: string;
  elements: DocumentElement[];
  updatedAt: string;
}

// GET - Load template structure
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: templateId } = await params;
    
    if (!templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    const structurePath = path.join(process.cwd(), 'data', 'structures', `${templateId}.json`);
    
    try {
      const structureData = await fs.readFile(structurePath, 'utf-8');
      const structure: TemplateStructure = JSON.parse(structureData);
      
      return NextResponse.json(structure);
    } catch (error) {
      // Structure file doesn't exist yet
      return NextResponse.json(
        { error: 'Structure not found' },
        { status: 404 }
      );
    }
  } catch (error) {
    console.error('Error loading template structure:', error);
    return NextResponse.json(
      { error: 'Failed to load template structure' },
      { status: 500 }
    );
  }
}

// POST - Save template structure
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: templateId } = await params;
    
    if (!templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    const { elements } = await request.json();

    if (!elements || !Array.isArray(elements)) {
      return NextResponse.json(
        { error: 'Elements array is required' },
        { status: 400 }
      );
    }

    // Ensure structures directory exists
    const structuresDir = path.join(process.cwd(), 'data', 'structures');
    try {
      await fs.access(structuresDir);
    } catch {
      await fs.mkdir(structuresDir, { recursive: true });
    }

    const structure: TemplateStructure = {
      templateId,
      elements,
      updatedAt: new Date().toISOString()
    };

    const structurePath = path.join(structuresDir, `${templateId}.json`);
    await fs.writeFile(structurePath, JSON.stringify(structure, null, 2));

    return NextResponse.json({ 
      success: true, 
      message: 'Template structure saved successfully' 
    });
  } catch (error) {
    console.error('Error saving template structure:', error);
    return NextResponse.json(
      { error: 'Failed to save template structure' },
      { status: 500 }
    );
  }
}

// DELETE - Delete template structure
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: templateId } = await params;
    
    if (!templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    const structurePath = path.join(process.cwd(), 'data', 'structures', `${templateId}.json`);
    
    try {
      await fs.unlink(structurePath);
      return NextResponse.json({ 
        success: true, 
        message: 'Template structure deleted successfully' 
      });
    } catch (error) {
      // File doesn't exist, that's fine
      return NextResponse.json({ 
        success: true, 
        message: 'Template structure was already deleted' 
      });
    }
  } catch (error) {
    console.error('Error deleting template structure:', error);
    return NextResponse.json(
      { error: 'Failed to delete template structure' },
      { status: 500 }
    );
  }
}
