"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowLeft, Save, Eye, Plus, Trash2 } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";

interface Template {
  id: string;
  name: string;
  description: string;
  fields: string[];
  filePath: string;
  uploadedAt: string;
}

interface DocumentElement {
  id: string;
  type: 'text' | 'field' | 'signature' | 'date';
  content: string;
  alignment: 'left' | 'center' | 'right' | 'justify';
  fontSize: number;
  fontWeight: 'normal' | 'bold';
  margin: {
    top: number;
    bottom: number;
  };
  textIndent: number;
}

export default function TemplateEditorPage() {
  const params = useParams();
  const router = useRouter();
  const templateName = params.templateName as string;
  
  const [template, setTemplate] = useState<Template | null>(null);
  const [elements, setElements] = useState<DocumentElement[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [previewContent, setPreviewContent] = useState<string>('');

  useEffect(() => {
    fetchTemplate();
  }, [templateName]);

  const fetchTemplate = async () => {
    try {
      const response = await fetch('/api/templates');
      const templates = await response.json();
      const currentTemplate = templates.find((t: Template) => t.id === templateName);
      
      if (currentTemplate) {
        setTemplate(currentTemplate);
        // Initialize with basic document structure if no saved structure exists
        await loadOrCreateDocumentStructure(currentTemplate);
      } else {
        toast.error('Template not found');
        router.push('/');
      }
    } catch (error) {
      console.error('Error fetching template:', error);
      toast.error('Failed to load template');
    } finally {
      setLoading(false);
    }
  };

  const loadOrCreateDocumentStructure = async (template: Template) => {
    try {
      // Try to load existing structure
      const response = await fetch(`/api/templates/${template.id}/structure`);
      
      if (response.ok) {
        const structure = await response.json();
        setElements(structure.elements || []);
      } else {
        // Create initial structure from DOCX
        await createInitialStructure(template);
      }
    } catch (error) {
      console.error('Error loading structure:', error);
      await createInitialStructure(template);
    }
  };

  const createInitialStructure = async (template: Template) => {
    try {
      // Get raw text from DOCX to create initial structure
      const response = await fetch('/api/templates/extract-text', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ templateId: template.id })
      });

      if (response.ok) {
        const { text } = await response.json();
        const lines = text.split('\n').filter((line: string) => line.trim());
        
        const initialElements: DocumentElement[] = lines.map((line: string, index: number) => ({
          id: `element-${index}`,
          type: detectElementType(line),
          content: line.trim(),
          alignment: detectAlignment(line),
          fontSize: detectFontSize(line),
          fontWeight: detectFontWeight(line),
          margin: { top: 12, bottom: 12 },
          textIndent: detectIndent(line)
        }));

        setElements(initialElements);
      }
    } catch (error) {
      console.error('Error creating initial structure:', error);
      // Fallback: create a basic structure
      setElements([
        {
          id: 'element-0',
          type: 'text',
          content: 'Click to edit this text',
          alignment: 'center',
          fontSize: 12,
          fontWeight: 'normal',
          margin: { top: 12, bottom: 12 },
          textIndent: 0
        }
      ]);
    }
  };

  const detectElementType = (line: string): DocumentElement['type'] => {
    if (line.includes('{') && line.includes('}')) return 'field';
    if (/affiant|signature|mayor|hon\./i.test(line)) return 'signature';
    if (/day of|date|sworn/i.test(line)) return 'date';
    return 'text';
  };

  const detectAlignment = (line: string): DocumentElement['alignment'] => {
    if (/^(republic|province|municipality|affidavit|certificate)/i.test(line.trim())) return 'center';
    if (/affiant|hon\.|mayor|ctc/i.test(line)) return 'center';
    if (line.length > 50) return 'justify';
    return 'left';
  };

  const detectFontSize = (line: string): number => {
    if (/affidavit|certificate/i.test(line)) return 14;
    return 12;
  };

  const detectFontWeight = (line: string): DocumentElement['fontWeight'] => {
    if (/affidavit|certificate|republic|witness whereof/i.test(line)) return 'bold';
    return 'normal';
  };

  const detectIndent = (line: string): number => {
    if (line.length > 50 && /^(i,|this is to certify|that)/i.test(line.trim())) return 36;
    if (/witness whereof|subscribed and sworn/i.test(line)) return 36;
    return 0;
  };

  const addElement = () => {
    const newElement: DocumentElement = {
      id: `element-${Date.now()}`,
      type: 'text',
      content: 'New text element',
      alignment: 'left',
      fontSize: 12,
      fontWeight: 'normal',
      margin: { top: 12, bottom: 12 },
      textIndent: 0
    };
    setElements([...elements, newElement]);
  };

  const updateElement = (id: string, updates: Partial<DocumentElement>) => {
    setElements(elements.map(el => el.id === id ? { ...el, ...updates } : el));
  };

  const deleteElement = (id: string) => {
    setElements(elements.filter(el => el.id !== id));
  };

  const moveElement = (id: string, direction: 'up' | 'down') => {
    const index = elements.findIndex(el => el.id === id);
    if (index === -1) return;

    const newElements = [...elements];
    const targetIndex = direction === 'up' ? index - 1 : index + 1;

    if (targetIndex >= 0 && targetIndex < elements.length) {
      [newElements[index], newElements[targetIndex]] = [newElements[targetIndex], newElements[index]];
      setElements(newElements);
    }
  };

  const generatePreview = () => {
    const html = elements.map(element => {
      const style = `
        text-align: ${element.alignment};
        font-size: ${element.fontSize}pt;
        font-weight: ${element.fontWeight};
        margin: ${element.margin.top}pt 0 ${element.margin.bottom}pt 0;
        text-indent: ${element.textIndent}pt;
        line-height: 1.5;
      `.trim();

      return `<p style="${style}">${element.content}</p>`;
    }).join('\n');

    setPreviewContent(html);
  };

  const saveStructure = async () => {
    if (!template) return;

    setSaving(true);
    try {
      const response = await fetch(`/api/templates/${template.id}/structure`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ elements })
      });

      if (response.ok) {
        toast.success('Template structure saved successfully!');
        generatePreview();
      } else {
        toast.error('Failed to save template structure');
      }
    } catch (error) {
      console.error('Error saving structure:', error);
      toast.error('An error occurred while saving');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#55a4ff] mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading template...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!template) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-muted-foreground">Template not found</p>
          <Link href="/">
            <Button className="mt-4">Go Home</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Link href="/upload-templates">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold">Edit Template: {template.name}</h1>
          <p className="text-muted-foreground">{template.description}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Editor Panel */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Document Structure</CardTitle>
                <CardDescription>
                  Edit the structure and formatting of your template
                </CardDescription>
              </div>
              <Button onClick={addElement} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Element
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4 max-h-[600px] overflow-y-auto">
            {elements.map((element, index) => (
              <ElementEditor
                key={element.id}
                element={element}
                index={index}
                onUpdate={updateElement}
                onDelete={deleteElement}
                onMove={moveElement}
                canMoveUp={index > 0}
                canMoveDown={index < elements.length - 1}
              />
            ))}
          </CardContent>
        </Card>

        {/* Preview Panel */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Preview</CardTitle>
                <CardDescription>
                  Live preview of your template
                </CardDescription>
              </div>
              <div className="flex gap-2">
                <Button onClick={generatePreview} variant="outline" size="sm">
                  <Eye className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
                <Button onClick={saveStructure} disabled={saving} size="sm">
                  <Save className="h-4 w-4 mr-2" />
                  {saving ? 'Saving...' : 'Save'}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg overflow-hidden bg-gray-100 p-4">
              <div
                className="bg-white shadow-lg mx-auto"
                style={{
                  width: "210mm",
                  minHeight: "297mm",
                  padding: "25.4mm",
                  fontFamily: "'Times New Roman', serif",
                  color: "#000",
                  transform: "scale(0.5)",
                  transformOrigin: "top center",
                }}
                dangerouslySetInnerHTML={{ __html: previewContent }}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Element Editor Component
interface ElementEditorProps {
  element: DocumentElement;
  index: number;
  onUpdate: (id: string, updates: Partial<DocumentElement>) => void;
  onDelete: (id: string) => void;
  onMove: (id: string, direction: 'up' | 'down') => void;
  canMoveUp: boolean;
  canMoveDown: boolean;
}

function ElementEditor({ element, index, onUpdate, onDelete, onMove, canMoveUp, canMoveDown }: ElementEditorProps) {
  return (
    <div className="border rounded-lg p-4 space-y-3">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium">Element {index + 1}</span>
        <div className="flex gap-1">
          <Button
            onClick={() => onMove(element.id, 'up')}
            disabled={!canMoveUp}
            variant="outline"
            size="sm"
          >
            ↑
          </Button>
          <Button
            onClick={() => onMove(element.id, 'down')}
            disabled={!canMoveDown}
            variant="outline"
            size="sm"
          >
            ↓
          </Button>
          <Button
            onClick={() => onDelete(element.id)}
            variant="outline"
            size="sm"
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-3">
        <div>
          <Label htmlFor={`type-${element.id}`}>Type</Label>
          <Select
            value={element.type}
            onValueChange={(value: DocumentElement['type']) => onUpdate(element.id, { type: value })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="text">Text</SelectItem>
              <SelectItem value="field">Field</SelectItem>
              <SelectItem value="signature">Signature</SelectItem>
              <SelectItem value="date">Date</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor={`alignment-${element.id}`}>Alignment</Label>
          <Select
            value={element.alignment}
            onValueChange={(value: DocumentElement['alignment']) => onUpdate(element.id, { alignment: value })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="left">Left</SelectItem>
              <SelectItem value="center">Center</SelectItem>
              <SelectItem value="right">Right</SelectItem>
              <SelectItem value="justify">Justify</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div>
        <Label htmlFor={`content-${element.id}`}>Content</Label>
        <Textarea
          id={`content-${element.id}`}
          value={element.content}
          onChange={(e) => onUpdate(element.id, { content: e.target.value })}
          rows={2}
        />
      </div>

      <div className="grid grid-cols-4 gap-2">
        <div>
          <Label htmlFor={`fontSize-${element.id}`}>Font Size</Label>
          <Input
            id={`fontSize-${element.id}`}
            type="number"
            value={element.fontSize}
            onChange={(e) => onUpdate(element.id, { fontSize: parseInt(e.target.value) || 12 })}
            min="8"
            max="24"
          />
        </div>

        <div>
          <Label htmlFor={`fontWeight-${element.id}`}>Weight</Label>
          <Select
            value={element.fontWeight}
            onValueChange={(value: DocumentElement['fontWeight']) => onUpdate(element.id, { fontWeight: value })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="normal">Normal</SelectItem>
              <SelectItem value="bold">Bold</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor={`marginTop-${element.id}`}>Top Margin</Label>
          <Input
            id={`marginTop-${element.id}`}
            type="number"
            value={element.margin.top}
            onChange={(e) => onUpdate(element.id, { 
              margin: { ...element.margin, top: parseInt(e.target.value) || 0 }
            })}
            min="0"
            max="48"
          />
        </div>

        <div>
          <Label htmlFor={`textIndent-${element.id}`}>Indent</Label>
          <Input
            id={`textIndent-${element.id}`}
            type="number"
            value={element.textIndent}
            onChange={(e) => onUpdate(element.id, { textIndent: parseInt(e.target.value) || 0 })}
            min="0"
            max="72"
          />
        </div>
      </div>
    </div>
  );
}
