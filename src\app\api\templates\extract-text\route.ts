import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import mammoth from 'mammoth';

export async function POST(request: NextRequest) {
  try {
    const { templateId } = await request.json();

    if (!templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    // Load template info
    const templatesPath = path.join(process.cwd(), 'data', 'templates.json');
    const templatesData = await fs.readFile(templatesPath, 'utf-8');
    const templates = JSON.parse(templatesData);
    
    const template = templates.find((t: any) => t.id === templateId);
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Read the DOCX file
    const docxPath = path.join(process.cwd(), template.filePath);
    const docxBuffer = await fs.readFile(docxPath);

    // Extract raw text using mammoth
    const result = await mammoth.extractRawText({ buffer: docxBuffer });
    
    return NextResponse.json({
      success: true,
      text: result.value,
      messages: result.messages
    });
  } catch (error) {
    console.error('Error extracting text from template:', error);
    return NextResponse.json(
      { error: 'Failed to extract text from template' },
      { status: 500 }
    );
  }
}
