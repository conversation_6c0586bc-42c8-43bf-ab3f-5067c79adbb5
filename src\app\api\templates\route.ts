import { NextResponse } from 'next/server';
import { getTemplates } from '@/lib/templates';

export async function GET() {
  try {
    const templates = await getTemplates();
    return NextResponse.json({ success: true, templates });
  } catch (error) {
    console.error('Error fetching templates:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch templates' },
      { status: 500 }
    );
  }
}
