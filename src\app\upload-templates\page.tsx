"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Upload, FileText, ArrowLeft } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";

export default function UploadTemplatesPage() {
  const [templateName, setTemplateName] = useState("");
  const [templateDescription, setTemplateDescription] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const router = useRouter();

  const validateAndSetFile = (file: File) => {
    if (!file.name.endsWith(".docx")) {
      toast.error("Please select a DOCX file");
      return false;
    }
    setSelectedFile(file);
    return true;
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      validateAndSetFile(file);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);

    const files = event.dataTransfer.files;
    if (files.length > 0) {
      validateAndSetFile(files[0]);
    }
  };

  const handleBrowseClick = () => {
    const fileInput = document.getElementById(
      "file-upload"
    ) as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!templateName || !templateDescription || !selectedFile) {
      toast.error("Please fill in all fields and select a file");
      return;
    }

    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append("templateName", templateName);
      formData.append("templateDescription", templateDescription);
      formData.append("file", selectedFile);

      const response = await fetch("/api/templates/upload", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        toast.success(
          "Template uploaded successfully! Redirecting to editor..."
        );
        setTemplateName("");
        setTemplateDescription("");
        setSelectedFile(null);
        // Reset file input
        const fileInput = document.getElementById(
          "file-upload"
        ) as HTMLInputElement;
        if (fileInput) fileInput.value = "";

        // Redirect to template editor
        setTimeout(() => {
          router.push(`/template-editor/${result.templateId}`);
        }, 1500);
      } else {
        toast.error(result.message || "Failed to upload template");
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("An error occurred while uploading the template");
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      {/* Page Header */}
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-semibold text-foreground mb-2">
          Upload Certificate Template
        </h1>
        <p className="text-muted-foreground text-lg">
          Add a new certificate template to the system
        </p>
      </div>

      {/* Upload Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Template Details
          </CardTitle>
          <CardDescription>
            Fill in the template information and upload your DOCX file
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* File Upload - Now at the top */}
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Upload className="h-4 w-4" />
                Upload DOCX Template
              </Label>

              {/* Drag and Drop Area */}
              <div
                className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  isDragOver
                    ? "border-primary bg-primary/5"
                    : "border-muted-foreground/25 hover:border-muted-foreground/50"
                } ${selectedFile ? "border-primary bg-primary/5" : ""}`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <div className="flex flex-col items-center gap-4">
                  <div className="p-3 rounded-full bg-muted">
                    <Upload className="h-8 w-8 text-muted-foreground" />
                  </div>

                  {selectedFile ? (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm font-medium text-primary">
                        <FileText className="h-4 w-4" />
                        {selectedFile.name}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        File selected successfully
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-foreground">
                        Click to upload or drag and drop
                      </p>
                      <p className="text-xs text-muted-foreground">
                        DOCX file required (.docx)
                      </p>
                    </div>
                  )}

                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleBrowseClick}
                  >
                    Browse Files
                  </Button>
                </div>

                {/* Hidden file input */}
                <input
                  id="file-upload"
                  type="file"
                  accept=".docx"
                  onChange={handleFileChange}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  required
                />
              </div>

              <p className="text-sm text-muted-foreground">
                Upload a DOCX file with placeholders in curly braces format:{" "}
                {"{name}, {date}, etc."}
              </p>
            </div>

            {/* Template Name */}
            <div className="space-y-2">
              <Label htmlFor="template-name">Template Name</Label>
              <Input
                id="template-name"
                type="text"
                placeholder="e.g., Good Moral Certificate"
                value={templateName}
                onChange={(e) => setTemplateName(e.target.value)}
                required
              />
            </div>

            {/* Template Description */}
            <div className="space-y-2">
              <Label htmlFor="template-description">Template Description</Label>
              <Textarea
                id="template-description"
                placeholder="e.g., Certificate of good moral character for students"
                value={templateDescription}
                onChange={(e) => setTemplateDescription(e.target.value)}
                rows={3}
                required
              />
            </div>

            {/* Submit Button */}
            <Button type="submit" className="w-full" disabled={isUploading}>
              {isUploading ? (
                <>
                  <Upload className="h-4 w-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Template
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
