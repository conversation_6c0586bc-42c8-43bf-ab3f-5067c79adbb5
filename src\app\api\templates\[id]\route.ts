import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import { deleteTemplate, getTemplates } from '@/lib/templates';

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const templateId = params.id;

    if (!templateId) {
      return NextResponse.json(
        { success: false, message: 'Template ID is required' },
        { status: 400 }
      );
    }

    // Get template info before deletion to clean up files
    const templates = await getTemplates();
    const template = templates.find(t => t.id === templateId);

    if (!template) {
      return NextResponse.json(
        { success: false, message: 'Template not found' },
        { status: 404 }
      );
    }

    // Delete template directory and all its contents
    const templateDir = path.join(process.cwd(), 'public', 'templates', templateId);

    try {
      await fs.rm(templateDir, { recursive: true, force: true });
    } catch (error) {
      console.warn('Error deleting template directory:', error);
      // Continue with JSON deletion even if file deletion fails
    }

    // Delete template page directory
    const pageDir = path.join(process.cwd(), 'src', 'app', 'pages', templateId);

    try {
      await fs.rm(pageDir, { recursive: true, force: true });
    } catch (error) {
      console.warn('Error deleting template page directory:', error);
      // Continue with JSON deletion even if page deletion fails
    }

    // Remove template from JSON file
    await deleteTemplate(templateId);

    return NextResponse.json({
      success: true,
      message: 'Template deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting template:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete template' },
      { status: 500 }
    );
  }
}
