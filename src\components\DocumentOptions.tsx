"use client";

import Link from "next/link";
import { FileText } from "lucide-react";
import { useEffect, useState } from "react";
import { Template } from "@/types/template";
import { DocumentOption } from "@/types/template";

export function DocumentOptions() {
  const [documents, setDocuments] = useState<DocumentOption[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        const response = await fetch("/api/templates");
        const result = await response.json();

        if (result.success) {
          const documentOptions: DocumentOption[] = result.templates.map(
            (template: Template) => ({
              title: template.name,
              description: template.description,
              href: `/fill/${template.id}`,
              icon: FileText,
            })
          );
          setDocuments(documentOptions);
        }
      } catch (error) {
        console.error("Error fetching templates:", error);
        // No fallback - rely entirely on JSON data
        setDocuments([]);
      } finally {
        setLoading(false);
      }
    };

    fetchTemplates();
  }, []);

  return (
    <div className="flex items-center justify-center min-h-screen px-4 py-8">
      <div className="flex flex-col items-center text-center w-full max-w-6xl">
        <div className="mb-8 space-y-2">
          <h1 className="text-3xl font-semibold text-foreground">Apply for?</h1>
          <p className="text-muted-foreground text-lg">
            Choose the document you need to apply for
          </p>
        </div>

        {/* Centered Card List */}
        <div className="flex flex-wrap justify-center gap-6 w-full">
          {loading ? (
            <div className="text-muted-foreground">Loading templates...</div>
          ) : documents.length === 0 ? (
            <div className="text-muted-foreground">No templates available</div>
          ) : (
            documents.map((document, index) => (
              <Link
                key={index}
                href={document.href}
                className="w-[280px] group relative bg-card hover:bg-card/80 border border-border rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-200 flex flex-col items-center text-center space-y-4 min-h-[180px] hover:scale-[1.02] active:scale-[0.98]"
              >
                <div className="p-4 rounded-lg bg-primary/10 group-hover:bg-primary/15 transition-colors duration-200">
                  <document.icon className="h-8 w-8 text-primary" />
                </div>

                <div className="space-y-2">
                  <h3 className="font-semibold text-lg text-card-foreground">
                    {document.title}
                  </h3>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {document.description}
                  </p>
                </div>
              </Link>
            ))
          )}
        </div>

        <p className="text-center text-sm text-muted-foreground mt-8 max-w-md">
          Click on any button to start your application process
        </p>
      </div>
    </div>
  );
}
