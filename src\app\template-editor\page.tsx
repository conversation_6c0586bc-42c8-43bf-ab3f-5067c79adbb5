"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Edit, Trash2, FileText, Calendar, Tag } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { Template } from "@/types/template";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

export default function TemplateEditorPage() {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const router = useRouter();

  const fetchTemplates = async () => {
    try {
      const response = await fetch('/api/templates');
      const result = await response.json();
      
      if (result.success) {
        setTemplates(result.templates);
      } else {
        toast.error("Failed to fetch templates");
      }
    } catch (error) {
      console.error('Error fetching templates:', error);
      toast.error("An error occurred while fetching templates");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTemplate = async (templateId: string, templateName: string) => {
    setDeletingId(templateId);
    
    try {
      const response = await fetch(`/api/templates/${templateId}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        toast.success(`Template "${templateName}" deleted successfully`);
        // Remove the deleted template from the local state
        setTemplates(prev => prev.filter(t => t.id !== templateId));
      } else {
        toast.error(result.message || "Failed to delete template");
      }
    } catch (error) {
      console.error('Delete error:', error);
      toast.error("An error occurred while deleting the template");
    } finally {
      setDeletingId(null);
    }
  };

  useEffect(() => {
    fetchTemplates();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">

      {/* Page Header */}
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-semibold text-foreground mb-2">
          Certificate Template Editor
        </h1>
        <p className="text-muted-foreground text-lg">
          Manage your certificate templates
        </p>
      </div>

      {/* Templates List */}
      {loading ? (
        <div className="text-center py-12">
          <div className="text-muted-foreground">Loading templates...</div>
        </div>
      ) : templates.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">No templates found</h3>
          <p className="text-muted-foreground mb-4">
            You haven't uploaded any certificate templates yet.
          </p>
          <Link href="/upload-templates">
            <Button>
              Upload Your First Template
            </Button>
          </Link>
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {templates.map((template) => (
            <Card key={template.id} className="relative">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="flex items-center gap-2 mb-2">
                      <FileText className="h-5 w-5 text-primary" />
                      {template.name}
                    </CardTitle>
                    <CardDescription>
                      {template.description}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {/* Upload Date */}
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    {formatDate(template.uploadedAt)}
                  </div>

                  {/* Placeholders */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Tag className="h-4 w-4" />
                      Placeholders ({template.placeholders.length})
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {template.placeholders.slice(0, 6).map((placeholder) => (
                        <span
                          key={placeholder}
                          className="inline-flex items-center px-2 py-1 rounded-md bg-primary/10 text-primary text-xs font-medium"
                        >
                          {"{" + placeholder + "}"}
                        </span>
                      ))}
                      {template.placeholders.length > 6 && (
                        <span className="inline-flex items-center px-2 py-1 rounded-md bg-muted text-muted-foreground text-xs">
                          +{template.placeholders.length - 6} more
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2 pt-4">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                    
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="text-destructive hover:text-destructive"
                          disabled={deletingId === template.id}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          {deletingId === template.id ? "Deleting..." : "Delete"}
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Template</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete the template "{template.name}"? 
                            This action cannot be undone and will permanently remove the template 
                            and all associated files.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDeleteTemplate(template.id, template.name)}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          >
                            Delete Template
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Add Template Button */}
      {templates.length > 0 && (
        <div className="text-center mt-8">
          <Link href="/upload-templates">
            <Button>
              Upload New Template
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
}
