import { promises as fs } from 'fs';
import path from 'path';
import { Template } from '@/types/template';

const TEMPLATES_FILE_PATH = path.join(process.cwd(), 'data', 'templates.json');

export interface TemplatesData {
  templates: Template[];
}

export async function getTemplates(): Promise<Template[]> {
  try {
    const fileContent = await fs.readFile(TEMPLATES_FILE_PATH, 'utf-8');
    const data: TemplatesData = JSON.parse(fileContent);
    return data.templates;
  } catch (error) {
    console.error('Error reading templates file:', error);
    return [];
  }
}

export async function addTemplate(template: Template): Promise<void> {
  try {
    const templates = await getTemplates();
    templates.push(template);
    
    const data: TemplatesData = { templates };
    await fs.writeFile(TEMPLATES_FILE_PATH, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error adding template:', error);
    throw new Error('Failed to add template');
  }
}

export async function updateTemplate(templateId: string, updatedTemplate: Partial<Template>): Promise<void> {
  try {
    const templates = await getTemplates();
    const index = templates.findIndex(t => t.id === templateId);
    
    if (index === -1) {
      throw new Error('Template not found');
    }
    
    templates[index] = { ...templates[index], ...updatedTemplate };
    
    const data: TemplatesData = { templates };
    await fs.writeFile(TEMPLATES_FILE_PATH, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error updating template:', error);
    throw new Error('Failed to update template');
  }
}

export async function deleteTemplate(templateId: string): Promise<void> {
  try {
    const templates = await getTemplates();
    const filteredTemplates = templates.filter(t => t.id !== templateId);
    
    const data: TemplatesData = { templates: filteredTemplates };
    await fs.writeFile(TEMPLATES_FILE_PATH, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error deleting template:', error);
    throw new Error('Failed to delete template');
  }
}

export function generateTemplateId(name: string): string {
  return name.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');
}

export function extractPlaceholders(content: string): string[] {
  const regex = /\{([^}]+)\}/g;
  const placeholders: string[] = [];
  let match;
  
  while ((match = regex.exec(content)) !== null) {
    const placeholder = match[1].trim();
    if (!placeholders.includes(placeholder)) {
      placeholders.push(placeholder);
    }
  }
  
  return placeholders;
}
